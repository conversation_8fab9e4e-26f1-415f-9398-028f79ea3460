# Liquidity Manager Contract

The Liquidity Manager is a smart contract that allows users to interact with multiple liquidity pools to mint and burn liquidity positions. It's designed to work with Uniswap V3-like pools that use tick-based concentrated liquidity.

## Features

- **Multi-Pool Support**: Interact with any pool by providing the pool address
- **Liquidity Minting**: Add liquidity to pools within specified tick ranges
- **Liquidity Burning**: Remove liquidity from existing positions
- **Fee Collection**: Collect accumulated fees from positions
- **Slippage Protection**: Built-in slippage protection for all operations
- **Multicall Support**: Execute multiple operations in a single transaction
- **Emergency Functions**: Emergency withdrawal capabilities

## Contract Structure

### Main Functions

#### `mintLiquidity(MintParams calldata params)`
Mints liquidity for a given pool and tick range.

**Parameters:**
- `pool`: Address of the target pool
- `recipient`: Address that will own the liquidity position
- `tickLower`: Lower bound of the tick range
- `tickUpper`: Upper bound of the tick range
- `liquidity`: Amount of liquidity to mint
- `amount0Desired`: Desired amount of token0 to deposit
- `amount1Desired`: Desired amount of token1 to deposit
- `amount0Min`: Minimum amount of token0 (slippage protection)
- `amount1Min`: Minimum amount of token1 (slippage protection)
- `deadline`: Transaction deadline

#### `burnLiquidity(BurnParams calldata params)`
Burns liquidity from an existing position.

**Parameters:**
- `pool`: Address of the target pool
- `tickLower`: Lower bound of the tick range
- `tickUpper`: Upper bound of the tick range
- `liquidity`: Amount of liquidity to burn
- `amount0Min`: Minimum amount of token0 to receive
- `amount1Min`: Minimum amount of token1 to receive
- `deadline`: Transaction deadline

#### `collectFees()`
Collects accumulated fees from a position.

#### `getPosition()`
Returns information about a specific position.

#### `getPoolInfo()`
Returns basic information about a pool (tokens, fee, tick spacing).

#### `calculateLiquidity()`
Calculates the liquidity amount for given token amounts and tick range.

## Usage Examples

### Minting Liquidity

```solidity
// Create mint parameters
LiquidityManager.MintParams memory params = LiquidityManager.MintParams({
    pool: 0x1234..., // Pool address
    recipient: msg.sender,
    tickLower: -887220, // Full range for example
    tickUpper: 887220,
    liquidity: 1000000,
    amount0Desired: 1 ether,
    amount1Desired: 1 ether,
    amount0Min: 0.9 ether, // 10% slippage tolerance
    amount1Min: 0.9 ether,
    deadline: block.timestamp + 3600 // 1 hour
});

// Approve tokens first
IERC20(token0).approve(address(liquidityManager), 1 ether);
IERC20(token1).approve(address(liquidityManager), 1 ether);

// Mint liquidity
(uint256 amount0, uint256 amount1) = liquidityManager.mintLiquidity(params);
```

### Burning Liquidity

```solidity
// Create burn parameters
LiquidityManager.BurnParams memory params = LiquidityManager.BurnParams({
    pool: 0x1234..., // Pool address
    tickLower: -887220,
    tickUpper: 887220,
    liquidity: 500000, // Half of the position
    amount0Min: 0.45 ether,
    amount1Min: 0.45 ether,
    deadline: block.timestamp + 3600
});

// Burn liquidity
(uint256 amount0, uint256 amount1) = liquidityManager.burnLiquidity(params);
```

### Collecting Fees

```solidity
// Collect all available fees
(uint256 amount0, uint256 amount1) = liquidityManager.collectFees(
    poolAddress,
    msg.sender, // recipient
    tickLower,
    tickUpper,
    type(uint128).max, // collect all token0 fees
    type(uint128).max  // collect all token1 fees
);
```

### Using Multicall

```solidity
// Prepare multiple function calls
bytes[] memory calls = new bytes[](2);
calls[0] = abi.encodeWithSelector(
    liquidityManager.mintLiquidity.selector,
    mintParams1
);
calls[1] = abi.encodeWithSelector(
    liquidityManager.mintLiquidity.selector,
    mintParams2
);

// Execute all calls in one transaction
bytes[] memory results = liquidityManager.multicall(calls);
```

## Security Considerations

1. **Slippage Protection**: Always set appropriate `amount0Min` and `amount1Min` values
2. **Deadline**: Use reasonable deadline values to prevent transaction replay
3. **Pool Validation**: The contract validates pool addresses, but ensure you're using correct pool addresses
4. **Token Approvals**: Approve the exact amounts needed before calling mint functions
5. **Emergency Functions**: The emergency withdraw function should have proper access control in production

## Integration Notes

1. **Pool Compatibility**: Works with any Uniswap V3-compatible pool
2. **Token Standards**: Supports standard ERC20 tokens
3. **Gas Optimization**: Use multicall for multiple operations to save gas
4. **Math Libraries**: In production, replace simplified math functions with proper Uniswap math libraries

## Testing

Run the tests using Foundry:

```bash
forge test --match-contract LiquidityManagerTest
```

## Production Considerations

1. **Access Control**: Add proper access control for emergency functions
2. **Math Libraries**: Use Uniswap's TickMath and LiquidityAmounts libraries
3. **Reentrancy Protection**: Add reentrancy guards where necessary
4. **Oracle Integration**: Consider price oracle integration for better slippage protection
5. **Fee Management**: Implement fee collection mechanisms if needed

## License

MIT License
