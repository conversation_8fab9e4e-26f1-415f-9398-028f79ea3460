# Manual Setup Guide for SEI Liquidity Manager Testing

If the automated scripts don't work, follow these manual steps:

## 1. Install Foundry

### Option A: Automatic Installation
```bash
curl -L https://foundry.paradigm.xyz | bash
source ~/.bashrc
foundryup
```

### Option B: Manual Installation
```bash
# Download foundryup
curl -L https://foundry.paradigm.xyz -o foundryup
chmod +x foundryup
./foundryup

# Add to PATH
echo 'export PATH="$PATH:$HOME/.foundry/bin"' >> ~/.bashrc
source ~/.bashrc
```

### Verify Installation
```bash
forge --version
anvil --version
```

## 2. Build the Project

```bash
cd contracts
forge build
```

If you get compilation errors, check:
- All interface files are present
- Import paths are correct
- Solidity version is compatible

## 3. Run Basic Tests

```bash
forge test --match-contract LiquidityManagerTest -v
```

Expected output:
```
Running 6 tests for test/LiquidityManager.t.sol:LiquidityManagerTest
[PASS] testBurnLiquidityParams() (gas: ...)
[PASS] testEmergencyWithdraw() (gas: ...)
[PASS] testExpiredDeadlineReverts() (gas: ...)
[PASS] testInvalidPoolReverts() (gas: ...)
[PASS] testInvalidTickRangeReverts() (gas: ...)
[PASS] testMintLiquidityParams() (gas: ...)
Test result: ok. 6 passed; 0 failed; finished in ...
```

## 4. Test with SEI Mainnet Fork

### Single Test
```bash
forge test --match-test testGetPoolInfo \
  --fork-url https://evm-rpc.sei-apis.com \
  -vvv
```

### All Integration Tests
```bash
forge test --match-contract LiquidityManagerIntegration \
  --fork-url https://evm-rpc.sei-apis.com \
  -v
```

## 5. Deploy to SEI Mainnet (Optional)

### Set Environment Variables
```bash
export PRIVATE_KEY=0x... # Your private key (without 0x prefix)
```

### Deploy
```bash
forge script script/DeployLiquidityManager.s.sol \
  --rpc-url https://evm-rpc.sei-apis.com \
  --broadcast
```

## Troubleshooting

### Issue: "forge: command not found"
**Solution**: 
```bash
export PATH="$PATH:$HOME/.foundry/bin"
source ~/.bashrc
```

### Issue: "environment variable not found"
**Solution**: Comment out etherscan config in foundry.toml:
```toml
# [etherscan]
# sei_mainnet = { key = "${SEI_ETHERSCAN_API_KEY}", url = "https://seistream.app/api" }
```

### Issue: "RPC connection failed"
**Solutions**:
1. Check internet connection
2. Try alternative RPC: `https://evm-rpc-testnet.sei-apis.com`
3. Use local anvil: `anvil --fork-url https://evm-rpc.sei-apis.com`

### Issue: "Pool not found"
**Solution**: Pool addresses might have changed. Get latest from:
```bash
curl -s "https://sei-api.dragonswap.app/api/v1/pools" | jq '.pools[0:5]'
```

### Issue: "Gas estimation failed"
**Solutions**:
1. Increase gas limit: `--gas-limit 3000000`
2. Use different pool address
3. Check if pool has liquidity

## Manual Testing Commands

### Test Pool Information
```bash
forge test --match-test testGetPoolInfo \
  --fork-url https://evm-rpc.sei-apis.com \
  -vvv
```

### Test Multiple Pools
```bash
forge test --match-test testGetPoolInfoMultiplePools \
  --fork-url https://evm-rpc.sei-apis.com \
  -vvv
```

### Test Pool State
```bash
forge test --match-test testPoolSlot0 \
  --fork-url https://evm-rpc.sei-apis.com \
  -vvv
```

### Test Token Balances
```bash
forge test --match-test testTokenBalances \
  --fork-url https://evm-rpc.sei-apis.com \
  -vvv
```

### Test Liquidity Calculation
```bash
forge test --match-test testCalculateLiquidity \
  --fork-url https://evm-rpc.sei-apis.com \
  -vvv
```

### Test Multicall
```bash
forge test --match-test testMulticall \
  --fork-url https://evm-rpc.sei-apis.com \
  -vvv
```

## Expected Test Results

### Successful Pool Info Test
```
Pool Info for USDC/WSEI:
Token0: 0x3894085Ef7Ff0f0aeDf52E2A2704928d1Ec074F1
Token1: 0xe30fedd158a2e3b13e9badaeabafc5516e95e8c7
Fee: 3000
Tick Spacing: 60
```

### Successful Pool State Test
```
Pool USDC/WSEI Slot0:
SqrtPriceX96: [large number]
Current Tick: [current tick]
Observation Index: [number]
Observation Cardinality: [number]
Fee Protocol: [number]
Unlocked: true
```

## Alternative Testing (Without Fork)

If forking doesn't work, you can test the contract logic:

```bash
# Test contract compilation and basic functionality
forge test --match-contract LiquidityManagerTest -v

# Test parameter validation
forge test --match-test testInvalidPoolReverts -v
forge test --match-test testInvalidTickRangeReverts -v
forge test --match-test testExpiredDeadlineReverts -v
```

## Getting Help

If you encounter issues:

1. Check Foundry documentation: https://book.getfoundry.sh/
2. SEI documentation: https://docs.sei.io/
3. DragonSwap API: https://sei-api.dragonswap.app/api/v1/pools
4. SEI Explorer: https://seistream.app/

## Success Criteria

Your setup is working correctly if:
- ✅ `forge build` compiles without errors
- ✅ Basic tests pass
- ✅ Fork tests can read pool information
- ✅ Contract functions work with real pool addresses
- ✅ No compilation or runtime errors
