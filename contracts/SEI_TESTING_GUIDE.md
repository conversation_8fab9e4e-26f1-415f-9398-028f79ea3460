# SEI Mainnet Testing Guide for Liquidity Manager

This guide explains how to test your Liquidity Manager contract against real SEI mainnet data using Anvil forking.

## 🔷 Overview

Your Liquidity Manager contract can interact with real SEI mainnet pools from DragonSwap and other DEXes. We'll test it using Foundry's forking capabilities to simulate real blockchain conditions.

## 📋 Prerequisites

1. **Install Foundry** (if not already installed):
   ```bash
   curl -L https://foundry.paradigm.xyz | bash
   foundryup
   ```

2. **Verify SEI mainnet connection**:
   ```bash
   curl -X POST -H "Content-Type: application/json" \
     --data '{"jsonrpc":"2.0","method":"eth_chainId","params":[],"id":1}' \
     https://evm-rpc.sei-apis.com
   ```
   Should return: `{"jsonrpc":"2.0","id":1,"result":"0x531"}`

## 🚀 Quick Start

### Option 1: Run the automated test script
```bash
cd contracts
./test_sei_mainnet.sh
```

### Option 2: Run individual tests manually

1. **Build the project**:
   ```bash
   forge build
   ```

2. **Run basic unit tests**:
   ```bash
   forge test --match-contract LiquidityManagerTest -v
   ```

3. **Run integration tests with SEI mainnet fork**:
   ```bash
   forge test --match-contract LiquidityManagerIntegration \
     --fork-url https://evm-rpc.sei-apis.com -vvv
   ```

## 🧪 Test Categories

### 1. Pool Information Tests
Tests reading basic pool data from real SEI pools:
```bash
forge test --match-test testGetPoolInfo \
  --fork-url https://evm-rpc.sei-apis.com -vvv
```

### 2. Multiple Pools Tests
Tests interaction with multiple real pools:
```bash
forge test --match-test testGetPoolInfoMultiplePools \
  --fork-url https://evm-rpc.sei-apis.com -vvv
```

### 3. Pool State Tests
Tests reading current pool state (prices, ticks, etc.):
```bash
forge test --match-test testPoolSlot0 \
  --fork-url https://evm-rpc.sei-apis.com -vvv
```

### 4. Token Balance Tests
Tests token interactions:
```bash
forge test --match-test testTokenBalances \
  --fork-url https://evm-rpc.sei-apis.com -vvv
```

### 5. Liquidity Calculation Tests
Tests liquidity math functions:
```bash
forge test --match-test testCalculateLiquidity \
  --fork-url https://evm-rpc.sei-apis.com -vvv
```

## 🏊 Real Pool Addresses Used in Tests

The tests use real DragonSwap pools on SEI mainnet:

| Pool | Address | Description |
|------|---------|-------------|
| USDC/WSEI | `******************************************` | High volume V3 pool |
| WBTC/USDC | `******************************************` | V3 pool |
| WSEI/USDC | `******************************************` | High volume V3 pool |

## 🪙 Token Addresses

| Token | Address | Decimals |
|-------|---------|----------|
| USDC | `******************************************` | 6 |
| WSEI | `******************************************` | 18 |
| WETH | `******************************************` | 18 |
| WBTC | `******************************************` | 8 |

## 🔧 Advanced Testing

### Testing with Real Liquidity Operations

To test actual minting/burning (requires tokens):

1. **Find a whale address** with tokens:
   ```bash
   # Use SEI block explorer to find addresses with large balances
   # Example: https://seistream.app/
   ```

2. **Impersonate the whale** in tests:
   ```solidity
   address whale = 0x...; // Address with tokens
   vm.startPrank(whale);
   IERC20(USDC).transfer(testUser, 1000000); // 1 USDC
   vm.stopPrank();
   ```

3. **Test actual minting**:
   ```solidity
   vm.startPrank(testUser);
   IERC20(USDC).approve(address(liquidityManager), 1000000);
   liquidityManager.mintLiquidity(params);
   vm.stopPrank();
   ```

### Gas Optimization Testing

Test gas usage:
```bash
forge test --match-contract LiquidityManagerIntegration \
  --fork-url https://evm-rpc.sei-apis.com \
  --gas-report
```

### Invariant Testing

Run invariant tests:
```bash
forge test --match-contract LiquidityManagerInvariant \
  --fork-url https://evm-rpc.sei-apis.com
```

## 🚀 Deployment to SEI Mainnet

### 1. Set up environment variables
```bash
export PRIVATE_KEY=0x... # Your private key
export SEI_ETHERSCAN_API_KEY=... # Optional, for verification
```

### 2. Deploy the contract
```bash
forge script script/DeployLiquidityManager.s.sol \
  --rpc-url https://evm-rpc.sei-apis.com \
  --broadcast \
  --verify
```

### 3. Verify deployment
```bash
forge verify-contract <CONTRACT_ADDRESS> LiquidityManager \
  --chain-id 1329 \
  --etherscan-api-key $SEI_ETHERSCAN_API_KEY
```

## 📊 Expected Test Results

When tests run successfully, you should see:

✅ **Pool Info Tests**: Contract can read pool data  
✅ **Token Tests**: Contract can interact with ERC20 tokens  
✅ **Calculation Tests**: Math functions work correctly  
✅ **Multicall Tests**: Batch operations work  
✅ **Emergency Tests**: Safety functions work  

## 🐛 Troubleshooting

### Common Issues

1. **RPC Connection Failed**:
   - Check internet connection
   - Try alternative RPC: `https://evm-rpc-testnet.sei-apis.com` for testnet

2. **Pool Address Not Found**:
   - Verify pool addresses are correct
   - Check if pools exist on current block

3. **Gas Estimation Failed**:
   - Pool might not support the operation
   - Check if pool has sufficient liquidity

4. **Token Transfer Failed**:
   - Ensure test addresses have token balances
   - Check token approvals

### Debug Commands

```bash
# Verbose output
forge test -vvvv

# Trace execution
forge test --trace

# Debug specific test
forge test --match-test testSpecificFunction --debug
```

## 🔗 Useful Resources

- [SEI Network Explorer](https://seistream.app/)
- [DragonSwap API](https://sei-api.dragonswap.app/api/v1/pools)
- [Foundry Documentation](https://book.getfoundry.sh/)
- [SEI EVM RPC](https://docs.sei.io/dev-interacting-with-sei/json-rpc)

## 🎯 Next Steps

1. Run all tests to ensure contract works with real SEI data
2. Deploy to SEI testnet first for additional testing
3. Deploy to SEI mainnet when ready
4. Integrate with your frontend application
5. Monitor contract performance and gas usage

Happy testing! 🚀
