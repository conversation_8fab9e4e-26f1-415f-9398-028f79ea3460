# Simplified Liquidity Manager - Mint Function Guide

The Liquidity Manager now features a simplified `mintLiquidity` function that automatically calculates the optimal liquidity based on the token amounts you provide.

## 🚀 New Simplified Interface

```solidity
function mintLiquidity(
    uint256 amount0Max,    // Maximum amount of token0 to use
    uint256 amount1Max,    // Maximum amount of token1 to use
    int24 tickLower,       // Lower tick of the position
    int24 tickUpper,       // Upper tick of the position
    address pool,          // Pool address
    address recipient      // Address to receive the liquidity position
) external returns (uint256 amount0, uint256 amount1);
```

## ✨ Key Features

- **No Struct Required**: Pass parameters directly
- **Automatic Liquidity Calculation**: Uses Uniswap V3 math to calculate optimal liquidity
- **Smart Token Usage**: Only uses the minimum required amounts
- **Automatic Refunds**: Returns excess tokens to the user
- **Real Math**: Implements actual Uniswap V3 tick and liquidity calculations

## 📋 How It Works

1. **You provide**: Maximum amounts of token0 and token1 you're willing to use
2. **Contract calculates**: Optimal liquidity based on current pool price and your amounts
3. **Contract uses**: Only the required amounts (often less than your maximums)
4. **Contract refunds**: Any excess tokens back to you

## 🎯 Usage Examples

### Example 1: Full Range Liquidity (Equal Exposure)

```solidity
// Mint liquidity across the full price range
liquidityManager.mintLiquidity(
    1000000,               // 1 USDC (6 decimals)
    1 ether,               // 1 WSEI (18 decimals)
    -887220,               // Full range lower
    887220,                // Full range upper
    USDC_WSEI_POOL,        // Pool address
    msg.sender             // Recipient
);
```

### Example 2: Concentrated Liquidity (Higher Fees)

```solidity
// Mint concentrated liquidity around current price
liquidityManager.mintLiquidity(
    1000000,               // 1 USDC
    1 ether,               // 1 WSEI
    -60,                   // Tight range lower
    60,                    // Tight range upper
    USDC_WSEI_POOL,        // Pool address
    msg.sender             // Recipient
);
```

### Example 3: Single-Sided Liquidity (USDC Only)

```solidity
// Mint liquidity with only USDC (when price is below range)
liquidityManager.mintLiquidity(
    1000000,               // 1 USDC
    0,                     // No WSEI
    -887220,               // Range below current price
    -443610,               // Range below current price
    USDC_WSEI_POOL,        // Pool address
    msg.sender             // Recipient
);
```

### Example 4: Single-Sided Liquidity (WSEI Only)

```solidity
// Mint liquidity with only WSEI (when price is above range)
liquidityManager.mintLiquidity(
    0,                     // No USDC
    1 ether,               // 1 WSEI
    443610,                // Range above current price
    887220,                // Range above current price
    USDC_WSEI_POOL,        // Pool address
    msg.sender             // Recipient
);
```

## 🔧 JavaScript/TypeScript Usage

```javascript
// Using ethers.js
const liquidityManager = new ethers.Contract(address, abi, signer);

// Example: Mint full range liquidity
const tx = await liquidityManager.mintLiquidity(
    ethers.utils.parseUnits("1", 6),    // 1 USDC
    ethers.utils.parseEther("1"),       // 1 WSEI
    -887220,                            // tickLower
    887220,                             // tickUpper
    "******************************************", // pool
    userAddress                         // recipient
);

await tx.wait();
console.log("Liquidity minted successfully!");
```

## 📊 Understanding Tick Ranges

| Range Type | tickLower | tickUpper | Description |
|------------|-----------|-----------|-------------|
| Full Range | -887220 | 887220 | Maximum possible range |
| Wide Range | -443610 | 443610 | Wide range around current price |
| Medium Range | -60 | 60 | Medium concentration |
| Tight Range | -10 | 10 | High concentration (higher fees) |

## 🎯 Pool Addresses on SEI Mainnet

| Pool | Address | Description |
|------|---------|-------------|
| USDC/WSEI | `******************************************` | High volume pool |
| WBTC/USDC | `******************************************` | Bitcoin/USD pool |
| WSEI/USDC | `******************************************` | Alternative WSEI pool |

## 🔍 What Happens Internally

1. **Pool State Reading**: Gets current price from pool's `slot0()`
2. **Tick Math**: Converts ticks to sqrt price ratios using Uniswap V3 math
3. **Liquidity Calculation**: Determines optimal liquidity based on:
   - Current pool price
   - Your tick range
   - Your maximum token amounts
4. **Token Transfer**: Transfers required amounts from you to the contract
5. **Pool Interaction**: Calls pool's `mint()` function
6. **Refund**: Returns any excess tokens to you

## ⚡ Gas Optimization Tips

1. **Use Multicall**: Mint in multiple pools in one transaction
2. **Approve Once**: Set high allowances to avoid repeated approvals
3. **Choose Ranges Wisely**: Tighter ranges = higher fees but more risk

## 🛡️ Safety Features

- **Input Validation**: Checks for valid pool addresses and tick ranges
- **Slippage Protection**: Built into the liquidity calculation
- **Automatic Refunds**: Excess tokens returned automatically
- **Error Handling**: Clear error messages for common issues

## 🚨 Important Notes

1. **Token Approvals**: Always approve tokens before calling `mintLiquidity`
2. **Tick Spacing**: Ensure your ticks align with the pool's tick spacing
3. **Price Impact**: Large amounts may affect pool price
4. **Impermanent Loss**: Understand the risks of providing liquidity

## 📈 Advanced Usage

### Multicall for Multiple Pools

```solidity
bytes[] memory calls = new bytes[](2);

calls[0] = abi.encodeWithSelector(
    liquidityManager.mintLiquidity.selector,
    1000000, 1 ether, -887220, 887220, pool1, msg.sender
);

calls[1] = abi.encodeWithSelector(
    liquidityManager.mintLiquidity.selector,
    500000, 0.5 ether, -60, 60, pool2, msg.sender
);

liquidityManager.multicall(calls);
```

### Dynamic Tick Calculation

```solidity
// Get current tick from pool
(, int24 currentTick, , , , , ) = IPool(pool).slot0();

// Create range around current tick
int24 tickLower = currentTick - 1000;
int24 tickUpper = currentTick + 1000;

liquidityManager.mintLiquidity(
    amount0Max, amount1Max, tickLower, tickUpper, pool, recipient
);
```

## 🎉 Benefits of the Simplified Interface

✅ **Easier to Use**: No complex structs  
✅ **Automatic Calculations**: No manual liquidity math  
✅ **Gas Efficient**: Optimized Uniswap V3 math  
✅ **Flexible**: Works with any tick range  
✅ **Safe**: Built-in validations and refunds  
✅ **Compatible**: Works with all Uniswap V3-like pools  

The simplified interface makes it much easier to integrate liquidity management into your dApps while maintaining all the power and flexibility of Uniswap V3! 🚀
