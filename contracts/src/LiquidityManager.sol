// SPDX-License-Identifier: MIT
pragma solidity ^0.8.13;

import "./interfaces/IPool.sol";
import "./interfaces/IERC20.sol";

/**
 * @title LiquidityManager
 * @dev A contract that manages liquidity across multiple pools
 * @notice This contract allows users to mint and burn liquidity positions across different pools
 */
contract LiquidityManager {
    // Events
    event LiquidityMinted(
        address indexed pool,
        address indexed recipient,
        int24 tickLower,
        int24 tickUpper,
        uint128 liquidity,
        uint256 amount0,
        uint256 amount1
    );

    event LiquidityBurned(
        address indexed pool,
        address indexed owner,
        int24 tickLower,
        int24 tickUpper,
        uint128 liquidity,
        uint256 amount0,
        uint256 amount1
    );

    // Errors
    error InvalidPool();
    error InvalidTickRange();
    error InsufficientLiquidity();
    error TransferFailed();
    error SlippageExceeded();

    // Struct to hold mint parameters
    struct MintParams {
        address pool;
        address recipient;
        int24 tickLower;
        int24 tickUpper;
        uint128 liquidity;
        uint256 amount0Desired;
        uint256 amount1Desired;
        uint256 amount0Min;
        uint256 amount1Min;
        uint256 deadline;
    }

    // Struct to hold burn parameters
    struct BurnParams {
        address pool;
        int24 tickLower;
        int24 tickUpper;
        uint128 liquidity;
        uint256 amount0Min;
        uint256 amount1Min;
        uint256 deadline;
    }

    // Modifier to check deadline
    modifier checkDeadline(uint256 deadline) {
        require(block.timestamp <= deadline, "Transaction too old");
        _;
    }

    /**
     * @notice Mints liquidity for a given pool and tick range
     * @param params The parameters for minting liquidity
     * @return amount0 The amount of token0 that was paid to mint the given amount of liquidity
     * @return amount1 The amount of token1 that was paid to mint the given amount of liquidity
     */
    function mintLiquidity(MintParams calldata params)
        external
        checkDeadline(params.deadline)
        returns (uint256 amount0, uint256 amount1)
    {
        // Validate inputs
        if (params.pool == address(0)) revert InvalidPool();
        if (params.tickLower >= params.tickUpper) revert InvalidTickRange();

        IPool pool = IPool(params.pool);

        // Get pool tokens
        address token0 = pool.token0();
        address token1 = pool.token1();

        // Transfer tokens from user to this contract
        if (params.amount0Desired > 0) {
            _safeTransferFrom(token0, msg.sender, address(this), params.amount0Desired);
            _safeApprove(token0, params.pool, params.amount0Desired);
        }

        if (params.amount1Desired > 0) {
            _safeTransferFrom(token1, msg.sender, address(this), params.amount1Desired);
            _safeApprove(token1, params.pool, params.amount1Desired);
        }

        // Mint liquidity
        (amount0, amount1) = pool.mint(
            params.recipient,
            params.tickLower,
            params.tickUpper,
            params.liquidity,
            abi.encode(msg.sender)
        );

        // Check slippage
        if (amount0 < params.amount0Min || amount1 < params.amount1Min) {
            revert SlippageExceeded();
        }

        // Refund excess tokens
        if (params.amount0Desired > amount0) {
            _safeTransfer(token0, msg.sender, params.amount0Desired - amount0);
        }
        if (params.amount1Desired > amount1) {
            _safeTransfer(token1, msg.sender, params.amount1Desired - amount1);
        }

        emit LiquidityMinted(
            params.pool,
            params.recipient,
            params.tickLower,
            params.tickUpper,
            params.liquidity,
            amount0,
            amount1
        );
    }

    /**
     * @notice Burns liquidity for a given pool and tick range
     * @param params The parameters for burning liquidity
     * @return amount0 The amount of token0 that was collected
     * @return amount1 The amount of token1 that was collected
     */
    function burnLiquidity(BurnParams calldata params)
        external
        checkDeadline(params.deadline)
        returns (uint256 amount0, uint256 amount1)
    {
        // Validate inputs
        if (params.pool == address(0)) revert InvalidPool();
        if (params.tickLower >= params.tickUpper) revert InvalidTickRange();
        if (params.liquidity == 0) revert InsufficientLiquidity();

        IPool pool = IPool(params.pool);

        // Burn liquidity
        (amount0, amount1) = pool.burn(params.tickLower, params.tickUpper, params.liquidity);

        // Check slippage
        if (amount0 < params.amount0Min || amount1 < params.amount1Min) {
            revert SlippageExceeded();
        }

        // Collect the tokens
        (uint256 collect0, uint256 collect1) = pool.collect(
            msg.sender,
            params.tickLower,
            params.tickUpper,
            uint128(amount0),
            uint128(amount1)
        );

        emit LiquidityBurned(
            params.pool,
            msg.sender,
            params.tickLower,
            params.tickUpper,
            params.liquidity,
            collect0,
            collect1
        );

        return (collect0, collect1);
    }

    /**
     * @notice Collects fees from a position
     * @param pool The pool address
     * @param recipient The recipient of the collected fees
     * @param tickLower The lower tick of the position
     * @param tickUpper The upper tick of the position
     * @param amount0Max The maximum amount of token0 to collect
     * @param amount1Max The maximum amount of token1 to collect
     * @return amount0 The amount of token0 collected
     * @return amount1 The amount of token1 collected
     */
    function collectFees(
        address pool,
        address recipient,
        int24 tickLower,
        int24 tickUpper,
        uint128 amount0Max,
        uint128 amount1Max
    ) external returns (uint256 amount0, uint256 amount1) {
        if (pool == address(0)) revert InvalidPool();
        if (tickLower >= tickUpper) revert InvalidTickRange();

        IPool poolContract = IPool(pool);

        (amount0, amount1) = poolContract.collect(
            recipient,
            tickLower,
            tickUpper,
            amount0Max,
            amount1Max
        );
    }

    /**
     * @notice Gets the position info for a given pool and tick range
     * @param pool The pool address
     * @param owner The owner of the position
     * @param tickLower The lower tick of the position
     * @param tickUpper The upper tick of the position
     * @return liquidity The amount of liquidity in the position
     * @return feeGrowthInside0LastX128 The fee growth inside the position for token0
     * @return feeGrowthInside1LastX128 The fee growth inside the position for token1
     * @return tokensOwed0 The amount of token0 owed to the position
     * @return tokensOwed1 The amount of token1 owed to the position
     */
    function getPosition(
        address pool,
        address owner,
        int24 tickLower,
        int24 tickUpper
    )
        external
        view
        returns (
            uint128 liquidity,
            uint256 feeGrowthInside0LastX128,
            uint256 feeGrowthInside1LastX128,
            uint128 tokensOwed0,
            uint128 tokensOwed1
        )
    {
        if (pool == address(0)) revert InvalidPool();

        IPool poolContract = IPool(pool);
        bytes32 positionKey = keccak256(abi.encodePacked(owner, tickLower, tickUpper));

        return poolContract.positions(positionKey);
    }

    /**
     * @notice Multicall function to execute multiple operations in a single transaction
     * @param data Array of encoded function calls
     * @return results Array of return data from each call
     */
    function multicall(bytes[] calldata data) external returns (bytes[] memory results) {
        results = new bytes[](data.length);
        for (uint256 i = 0; i < data.length; i++) {
            (bool success, bytes memory result) = address(this).delegatecall(data[i]);
            require(success, "Multicall failed");
            results[i] = result;
        }
    }

    // Internal utility functions
    function _safeTransfer(address token, address to, uint256 amount) internal {
        (bool success, bytes memory data) = token.call(
            abi.encodeWithSelector(IERC20.transfer.selector, to, amount)
        );
        require(success && (data.length == 0 || abi.decode(data, (bool))), "Transfer failed");
    }

    function _safeTransferFrom(address token, address from, address to, uint256 amount) internal {
        (bool success, bytes memory data) = token.call(
            abi.encodeWithSelector(IERC20.transferFrom.selector, from, to, amount)
        );
        require(success && (data.length == 0 || abi.decode(data, (bool))), "TransferFrom failed");
    }

    function _safeApprove(address token, address spender, uint256 amount) internal {
        (bool success, bytes memory data) = token.call(
            abi.encodeWithSelector(IERC20.approve.selector, spender, amount)
        );
        require(success && (data.length == 0 || abi.decode(data, (bool))), "Approve failed");
    }

    /**
     * @notice Emergency function to recover stuck tokens
     * @param token The token address to recover
     * @param to The recipient address
     * @param amount The amount to recover
     */
    function emergencyWithdraw(address token, address to, uint256 amount) external {
        // In a production environment, this should have proper access control
        // For now, anyone can call it - add onlyOwner modifier in production
        _safeTransfer(token, to, amount);
    }

    /**
     * @notice Get pool information
     * @param pool The pool address
     * @return token0 The first token of the pool
     * @return token1 The second token of the pool
     * @return fee The fee tier of the pool
     * @return tickSpacing The tick spacing of the pool
     */
    function getPoolInfo(address pool)
        external
        view
        returns (
            address token0,
            address token1,
            uint24 fee,
            int24 tickSpacing
        )
    {
        if (pool == address(0)) revert InvalidPool();

        IPool poolContract = IPool(pool);
        token0 = poolContract.token0();
        token1 = poolContract.token1();
        fee = poolContract.fee();
        tickSpacing = poolContract.tickSpacing();
    }

    /**
     * @notice Calculate the amount of liquidity for given token amounts
     * @param pool The pool address
     * @param tickLower The lower tick
     * @param tickUpper The upper tick
     * @param amount0 The amount of token0
     * @param amount1 The amount of token1
     * @return liquidity The calculated liquidity amount
     */
    function calculateLiquidity(
        address pool,
        int24 tickLower,
        int24 tickUpper,
        uint256 amount0,
        uint256 amount1
    ) external view returns (uint128 liquidity) {
        if (pool == address(0)) revert InvalidPool();
        if (tickLower >= tickUpper) revert InvalidTickRange();

        // This is a simplified calculation - in production, you'd want to use
        // the actual Uniswap V3 math libraries for precise calculations
        IPool poolContract = IPool(pool);
        (uint160 sqrtPriceX96, , , , , , ) = poolContract.slot0();

        // Simplified liquidity calculation
        // In production, use proper math libraries like Uniswap's LiquidityAmounts
        uint160 sqrtRatioAX96 = _getSqrtRatioAtTick(tickLower);
        uint160 sqrtRatioBX96 = _getSqrtRatioAtTick(tickUpper);

        if (sqrtPriceX96 <= sqrtRatioAX96) {
            liquidity = _getLiquidityForAmount0(sqrtRatioAX96, sqrtRatioBX96, amount0);
        } else if (sqrtPriceX96 < sqrtRatioBX96) {
            uint128 liquidity0 = _getLiquidityForAmount0(sqrtPriceX96, sqrtRatioBX96, amount0);
            uint128 liquidity1 = _getLiquidityForAmount1(sqrtRatioAX96, sqrtPriceX96, amount1);
            liquidity = liquidity0 < liquidity1 ? liquidity0 : liquidity1;
        } else {
            liquidity = _getLiquidityForAmount1(sqrtRatioAX96, sqrtRatioBX96, amount1);
        }
    }

    // Simplified math functions - in production, use Uniswap's math libraries
    function _getSqrtRatioAtTick(int24 tick) internal pure returns (uint160) {
        // Simplified implementation - use proper TickMath library in production
        return uint160(2**96); // Placeholder
    }

    function _getLiquidityForAmount0(uint160 sqrtRatioAX96, uint160 sqrtRatioBX96, uint256 amount0)
        internal
        pure
        returns (uint128)
    {
        // Simplified implementation - use proper LiquidityAmounts library in production
        return uint128(amount0); // Placeholder
    }

    function _getLiquidityForAmount1(uint160 sqrtRatioAX96, uint160 sqrtRatioBX96, uint256 amount1)
        internal
        pure
        returns (uint128)
    {
        // Simplified implementation - use proper LiquidityAmounts library in production
        return uint128(amount1); // Placeholder
    }
}