import { Card, CardContent } from "@/components/ui/card"

interface ErrorDisplayProps {
    title?: string
    message: string
    className?: string
}

export const ErrorDisplay = ({
    title = "Error",
    message,
    className = ""
}: ErrorDisplayProps) => (
    <Card className={`bg-red-900/20 border-red-800 ${className}`}>
        <CardContent className="p-6">
            <div className="flex items-center space-x-2">
                <div className="text-red-400">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                </div>
                <div>
                    <p className="text-red-400 font-medium">{title}</p>
                    <p className="text-red-300 text-sm">{message}</p>
                </div>
            </div>
        </CardContent>
    </Card>
)