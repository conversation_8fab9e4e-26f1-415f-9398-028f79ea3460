{"name": "mcp-client", "version": "1.0.0", "type": "module", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js"}, "dependencies": {"@langchain/core": "^0.3.62", "@langchain/groq": "^0.2.3", "@modelcontextprotocol/sdk": "^1.15.1", "axios": "^1.11.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "ethers": "^6.11.1", "express": "^4.19.2", "langchain": "^0.3.30", "node-fetch": "^3.3.2", "openai": "^4.25.0", "zod": "^3.22.4"}}